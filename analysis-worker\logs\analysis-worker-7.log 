{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","userEmail":"<EMAIL>","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:24","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","useBatch":true,"userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"batched":true,"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"batched":true,"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"batched":true,"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"batched":true,"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"batched":true,"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"batched":true,"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"batched":true,"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"batched":true,"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"batched":true,"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"batched":true,"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"jobId":"9e352f3f-2565-401a-9947-93a76baf74e2","level":"info","message":"Assessment job processed successfully","processingTime":"80334ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f990ae12-cbb7-45b8-b127-837ee06f57ef","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"jobId":"1e096017-fffd-42df-ab40-dce2b6a0215e","level":"info","message":"Assessment job processed successfully","processingTime":"80339ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1d64c8a4-8602-4b0f-bca2-bd7d0d25c1a0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:44","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"jobId":"f09ad89d-e6c1-4e6e-9424-d15fa4c5db43","level":"info","message":"Assessment job processed successfully","processingTime":"80338ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"e93b58a1-11d1-4daa-8f12-9bd5e5678c0f","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"jobId":"532ce33f-6329-49a9-b126-fa5e7b4d7833","level":"info","message":"Assessment job processed successfully","processingTime":"80341ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"43b3a045-e01f-481f-a2fd-91384e965c50","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"jobId":"dec5b295-b64e-4e30-8b2b-9dfa86d844aa","level":"info","message":"Assessment job processed successfully","processingTime":"80341ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"f6416d9e-fb19-4b8d-b50d-82ac478959d9","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"jobId":"5ea11ece-a108-4f95-a106-917e9293da3e","level":"info","message":"Assessment job processed successfully","processingTime":"80346ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"fceb86e5-c70a-4a61-bcd4-7960c7487f20","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"jobId":"296b250d-8310-4d12-98c6-1a30a2d62376","level":"info","message":"Assessment job processed successfully","processingTime":"80344ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"4971ee6b-2326-4aad-9367-004453217d94","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"jobId":"24d48424-2d35-4875-8831-47a42483e794","level":"info","message":"Assessment job processed successfully","processingTime":"80351ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"d9f2dc8a-5335-4fcc-b4eb-5480e52b4aba","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"jobId":"39ebfd93-79e1-42c6-a17e-668f4736ba79","level":"info","message":"Assessment job processed successfully","processingTime":"80351ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"1bc6f2ed-a15b-45ff-a95e-a76e7d43bf2e","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"jobId":"03c8e2e7-056c-46db-a13f-8f8a59b68d89","level":"info","message":"Assessment job processed successfully","processingTime":"80351ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:44","userId":"cc71c90b-f74a-4221-bbae-83faaf9c3bba","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:24","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:54","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:54","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:54","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:54","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:24","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:54","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:35","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:35","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:36","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:36","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:36","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:06","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:06","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Analysis result saved successfully","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Analysis result saved to Archive Service","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Analysis result saved successfully","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Analysis result saved to Archive Service","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"error","message":"Failed to update assessment job status","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Assessment job status updated","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"error","message":"Failed to update assessment job status","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Assessment job status updated","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Analysis complete notification sent","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Analysis completion notification sent","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"2622e8dd-3b68-4073-98cf-bd0671819523","level":"info","message":"Assessment job processed successfully","processingTime":"82204ms","resultId":"db945d94-0fe6-4b2b-b7c3-653f83431df7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"bffa3806-31dd-4b6f-8077-a91bd3517e28","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Analysis complete notification sent","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Analysis completion notification sent","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"jobId":"02fab328-ea00-4616-82aa-4d9ba9063766","level":"info","message":"Assessment job processed successfully","processingTime":"82158ms","resultId":"3350e424-9558-4c20-91f3-7721bd6cbc31","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"69417801-fcbc-45e6-be2e-78ec28f31699","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Analysis result saved successfully","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Analysis result saved to Archive Service","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Analysis result saved successfully","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Analysis result saved to Archive Service","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"error","message":"Failed to update assessment job status","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Assessment job status updated","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Analysis complete notification sent","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Analysis completion notification sent","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"jobId":"4919765c-01a3-43ae-a85b-1a6de7e8e7d7","level":"info","message":"Assessment job processed successfully","processingTime":"81990ms","resultId":"511bfac8-8afe-4a8f-bd10-155d156705a9","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f1e09dd9-2f00-4028-9c76-04a5e7819f02","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"error","message":"Failed to update assessment job status","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Assessment job status updated","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Analysis complete notification sent","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Analysis completion notification sent","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"jobId":"893013bd-bd21-46c1-aa21-e829b30a2133","level":"info","message":"Assessment job processed successfully","processingTime":"81981ms","resultId":"47000b29-29f4-4d9c-8919-27daaf0503be","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"30b316a9-eeb3-46ab-8cab-015e49f9c66c","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Analysis result saved successfully","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Analysis result saved to Archive Service","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Analysis result saved successfully","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Analysis result saved to Archive Service","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"error","message":"Failed to update assessment job status","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Assessment job status updated","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Analysis complete notification sent","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Analysis completion notification sent","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"jobId":"edc6d4e9-7ffe-447c-b474-945abc433927","level":"info","message":"Assessment job processed successfully","processingTime":"82092ms","resultId":"8b86bedb-2471-4d9c-a714-7eee55df9ad1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0d59f487-2919-4f15-ae44-d07b12c8c042","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"error","message":"Failed to update assessment job status","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Assessment job status updated","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Analysis complete notification sent","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Analysis completion notification sent","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"jobId":"597195b5-a089-4032-bb59-8e6b8e23bf08","level":"info","message":"Assessment job processed successfully","processingTime":"82015ms","resultId":"ae87505a-647f-4a3d-9167-c45532e6331c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"97d20a6b-f083-4314-b822-60d389bcdd3f","version":"1.0.0"}
{"archetype":"The Innovative Thinker","careerCount":5,"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Innovative Thinker","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Analysis result saved successfully","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Analysis result saved to Archive Service","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Analysis result saved successfully","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Analysis result saved to Archive Service","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"error","message":"Failed to update assessment job status","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Assessment job status updated","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Analysis complete notification sent","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Analysis completion notification sent","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"jobId":"6dae17c9-93c6-49cc-bf4e-462797fadc25","level":"info","message":"Assessment job processed successfully","processingTime":"82051ms","resultId":"c24330ca-df65-4e27-976e-5f73f8754508","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"bfd8fc39-716e-4f1e-bd35-df9760e4c8af","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"error","message":"Failed to update assessment job status","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Assessment job status updated","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Analysis complete notification sent","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Analysis completion notification sent","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"jobId":"4fe301d3-3eaf-43b9-a33d-4dda19069181","level":"info","message":"Assessment job processed successfully","processingTime":"81997ms","resultId":"b6d656a6-2256-462c-b370-d03a092b0bb0","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"6e732edd-8b10-467f-b2b6-da208fa03edd","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Analysis result saved successfully","resultId":"e9986490-be74-4af9-b856-548839d02ddb","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Analysis result saved to Archive Service","resultId":"e9986490-be74-4af9-b856-548839d02ddb","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Analysis result saved successfully","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Analysis result saved to Archive Service","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"error","message":"Failed to update assessment job status","resultId":"e9986490-be74-4af9-b856-548839d02ddb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Assessment job status updated","resultId":"e9986490-be74-4af9-b856-548839d02ddb","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Analysis complete notification sent","resultId":"e9986490-be74-4af9-b856-548839d02ddb","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Analysis completion notification sent","resultId":"e9986490-be74-4af9-b856-548839d02ddb","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"jobId":"9c7d00f0-e23f-425c-a500-fd077bf33a7d","level":"info","message":"Assessment job processed successfully","processingTime":"82116ms","resultId":"e9986490-be74-4af9-b856-548839d02ddb","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"71dd9335-2b69-4373-8627-572f545f300a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"error","message":"Failed to update assessment job status","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Assessment job status updated","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Analysis complete notification sent","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Analysis completion notification sent","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"jobId":"7f8d09f7-4c8e-446c-8170-e402d6e255da","level":"info","message":"Assessment job processed successfully","processingTime":"82077ms","resultId":"6f241d8d-6def-4f8c-b34f-252690cd5f5c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"24559705-143e-4f37-938b-155ec8d24ed9","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:36","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Analysis result saved successfully","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Analysis result saved to Archive Service","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Analysis result saved successfully","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Analysis result saved to Archive Service","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"error","message":"Failed to update assessment job status","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Assessment job status updated","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"error","message":"Failed to update assessment job status","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Assessment job status updated","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Analysis complete notification sent","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Analysis completion notification sent","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"54936e17-6312-4b6c-a23e-1fb92bc59ce8","level":"info","message":"Assessment job processed successfully","processingTime":"82069ms","resultId":"8a69889d-debc-43ee-a84b-d20b0a7f326d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"972b2967-ab86-4d56-b422-f045aea0eae5","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Analysis complete notification sent","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Analysis completion notification sent","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"jobId":"83d448cc-8113-4753-baab-666d3f9b293e","level":"info","message":"Assessment job processed successfully","processingTime":"82059ms","resultId":"7b4025fe-0fcc-4b9a-9f2d-fffd9adaf169","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"a35532cb-0929-4dd6-9c7f-54ff5277c7e4","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Analysis result saved successfully","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Analysis result saved to Archive Service","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Analysis result saved successfully","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Analysis result saved to Archive Service","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"error","message":"Failed to update assessment job status","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Assessment job status updated","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Analysis complete notification sent","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Analysis completion notification sent","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"jobId":"2641f27c-d708-40a3-920e-4439618514e1","level":"info","message":"Assessment job processed successfully","processingTime":"82046ms","resultId":"a45060b6-98c5-4af2-96e9-fd4e64df12d1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"fcc8fa72-369c-447e-a463-603c2bbc6d5c","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"error","message":"Failed to update assessment job status","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Assessment job status updated","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Analysis complete notification sent","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Analysis completion notification sent","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"jobId":"114b9c5e-fcaa-4005-9e98-9260dd552c0a","level":"info","message":"Assessment job processed successfully","processingTime":"82037ms","resultId":"1acee53d-f6ff-4138-a1ee-b0b1d35e8301","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"71a37d10-9ba9-4106-b1b4-d9a610a3261a","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Analysis result saved successfully","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Analysis result saved to Archive Service","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"error","message":"Failed to update assessment job status","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Assessment job status updated","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Analysis complete notification sent","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Analysis completion notification sent","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"jobId":"b8533328-53d0-46ee-939c-b91ec2d67caf","level":"info","message":"Assessment job processed successfully","processingTime":"82020ms","resultId":"ae2d9678-291c-4c6b-b856-6a6274226641","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"19e82309-afd9-49d7-b04f-2a921840bacf","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:06","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:36","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Analysis result saved successfully","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Analysis result saved to Archive Service","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Analysis result saved successfully","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Analysis result saved to Archive Service","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"error","message":"Failed to update assessment job status","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Assessment job status updated","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"error","message":"Failed to update assessment job status","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Assessment job status updated","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Analysis complete notification sent","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Analysis completion notification sent","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"1dc8ead0-ad6f-4b9a-9be2-37d7bfee1512","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","resultId":"53668c63-27bb-4ed5-88ff-350fed93ce57","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"4d672a0f-0cf4-42c1-87a2-043ae181bfa3","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Analysis complete notification sent","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Analysis completion notification sent","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"jobId":"3ea1b205-4b15-49e7-9987-5f8882cd670d","level":"info","message":"Assessment job processed successfully","processingTime":"82030ms","resultId":"9eeae94b-ca69-4671-9879-2d88a4d60488","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"eb3a0ac6-9cf6-4641-84f2-ffe1f73d1c63","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Analysis result saved successfully","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Analysis result saved to Archive Service","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Analysis result saved successfully","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Analysis result saved to Archive Service","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"error","message":"Failed to update assessment job status","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Assessment job status updated","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"error","message":"Failed to update assessment job status","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Assessment job status updated","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Analysis complete notification sent","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Analysis completion notification sent","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"f3fc6733-1856-4b5c-a576-b16646a29dcc","level":"info","message":"Assessment job processed successfully","processingTime":"82145ms","resultId":"2da96bee-2d55-48fc-bfd2-e45d4fd19987","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"1603b8d7-7ff0-48b5-b386-ea7faef59415","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Analysis complete notification sent","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Analysis completion notification sent","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"jobId":"002f9a09-ff97-44d6-ac58-f31292eb5c23","level":"info","message":"Assessment job processed successfully","processingTime":"82045ms","resultId":"24720b92-6b11-4b45-a2a9-f72819afd9aa","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7d393152-e4ce-4ef0-a952-2ee5c19db89e","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userEmail":"<EMAIL>","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","useMockModel":true,"version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Analysis result saved successfully","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Analysis result saved to Archive Service","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Analysis result saved successfully","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Analysis result saved to Archive Service","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"error","message":"Failed to update assessment job status","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Assessment job status updated","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Analysis complete notification sent","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Analysis completion notification sent","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"jobId":"01437391-2a08-4b71-8939-0576800c81c8","level":"info","message":"Assessment job processed successfully","processingTime":"81967ms","resultId":"d0157f04-0c84-40a5-bef4-a5a7920a5b06","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"7347498c-f8d6-40f6-9197-7103823a70d7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"error","message":"Failed to update assessment job status","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Assessment job status updated","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Analysis complete notification sent","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Analysis completion notification sent","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"jobId":"03929e97-5b31-46e1-8215-0aa10ba38c1a","level":"info","message":"Assessment job processed successfully","processingTime":"81971ms","resultId":"1103a046-5e10-4d76-9909-74f45a0c80db","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"52e6c7f0-ac56-4085-ba74-7a545cbfa2ce","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Analysis result saved successfully","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Analysis result saved to Archive Service","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Analysis result saved successfully","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Analysis result saved to Archive Service","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"error","message":"Failed to update assessment job status","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Assessment job status updated","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Analysis complete notification sent","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Analysis completion notification sent","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"jobId":"8d0be093-d992-4caf-8156-5af1f0ca7a80","level":"info","message":"Assessment job processed successfully","processingTime":"82062ms","resultId":"66a36d08-12ef-432d-ad9c-0f4c7ce978e7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"9fe403e0-af10-4847-bc6f-39f3644d163e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"error","message":"Failed to update assessment job status","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Assessment job status updated","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Analysis complete notification sent","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Analysis completion notification sent","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"jobId":"fc8bf4cf-0975-4e10-9e85-ecb49e52a6af","level":"info","message":"Assessment job processed successfully","processingTime":"82064ms","resultId":"11b39a6e-4213-4a0b-b6a5-bd38fafa7923","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"12583472-231b-4b5b-9819-bb1e7636956f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Analysis result saved successfully","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Analysis result saved to Archive Service","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Analysis result saved successfully","resultId":"0d1da394-357d-47f4-8590-0a657973d661","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Analysis result saved to Archive Service","resultId":"0d1da394-357d-47f4-8590-0a657973d661","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"error","message":"Failed to update assessment job status","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Assessment job status updated","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Analysis complete notification sent","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Analysis completion notification sent","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"jobId":"97c589be-f39e-4649-94d2-ecce9b8855e2","level":"info","message":"Assessment job processed successfully","processingTime":"82083ms","resultId":"eb8a2ff5-94d1-4fd7-800d-2292919e6faa","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"b83e9dbd-97a0-49c4-b384-33ffb4ff11c3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"error","message":"Failed to update assessment job status","resultId":"0d1da394-357d-47f4-8590-0a657973d661","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Assessment job status updated","resultId":"0d1da394-357d-47f4-8590-0a657973d661","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Analysis complete notification sent","resultId":"0d1da394-357d-47f4-8590-0a657973d661","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Analysis completion notification sent","resultId":"0d1da394-357d-47f4-8590-0a657973d661","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"jobId":"19c081b5-69ef-49b6-ae99-c1988e74b11a","level":"info","message":"Assessment job processed successfully","processingTime":"82070ms","resultId":"0d1da394-357d-47f4-8590-0a657973d661","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"2f0785b5-45e7-4d79-b572-0f79f4692fdf","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:36","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:06","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Analysis result saved successfully","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Analysis result saved to Archive Service","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Analysis result saved successfully","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Analysis result saved to Archive Service","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"error","message":"Failed to update assessment job status","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Assessment job status updated","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"error","message":"Failed to update assessment job status","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Assessment job status updated","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Analysis complete notification sent","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Analysis completion notification sent","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"9c8957c6-b9ad-4709-bc1f-23ecd251d634","level":"info","message":"Assessment job processed successfully","processingTime":"82112ms","resultId":"06ee719f-f5a6-4551-9573-c29637eb230b","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"94139e68-1c64-41ea-8b5f-3e3dfb5f0646","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Analysis complete notification sent","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Analysis completion notification sent","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"jobId":"1dbb63ce-ed33-4175-b731-b7a02a87585b","level":"info","message":"Assessment job processed successfully","processingTime":"82122ms","resultId":"e22852e0-c2bd-46eb-99ce-c60682003171","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"47553720-1571-4551-bc1d-4fe2c036d35f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:09","version":"1.0.0","weaknessesCount":3}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:09","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Analysis result saved successfully","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:11","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Analysis result saved to Archive Service","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"error","message":"Failed to update assessment job status","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:11","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Assessment job status updated","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Analysis complete notification sent","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Analysis completion notification sent","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"jobId":"e33cd46a-e484-4414-94dc-e0dee41adef6","level":"info","message":"Assessment job processed successfully","processingTime":"82010ms","resultId":"185ca825-020e-4c88-821d-eb0a830c29ba","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:11","userId":"89af13ff-47b0-48da-ac87-dce921a107a7","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:06","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:36","version":"1.0.0"}
