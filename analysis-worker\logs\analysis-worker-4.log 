{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","userEmail":"<EMAIL>","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","useMockModel":true,"version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:18","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:38","version":"1.0.0","weaknessesCount":3}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","useBatch":true,"userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"batched":true,"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"batched":true,"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"batched":true,"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"batched":true,"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"batched":true,"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"batched":true,"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"batched":true,"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"batched":true,"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"batched":true,"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"batched":true,"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"jobId":"ccc90e7d-99ec-4cef-80f8-b7292db47473","level":"info","message":"Assessment job processed successfully","processingTime":"80152ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"61a468f6-3b45-4420-b3e3-985b4e89daf3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:38","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"jobId":"cd57ebf0-25e9-494e-8cb9-edb9e22644a9","level":"info","message":"Assessment job processed successfully","processingTime":"80153ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9d705ec9-e7ab-49d6-938f-ede0df71ab73","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"jobId":"d9d33d00-040a-4b03-a13c-ced3510a85cc","level":"info","message":"Assessment job processed successfully","processingTime":"80153ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b56b1b26-ceec-40e4-b529-96a7aca326db","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"jobId":"ac8488da-0a08-4d8a-a4d3-d646d68ec4f5","level":"info","message":"Assessment job processed successfully","processingTime":"80149ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"9593d595-424f-4603-ae5a-a68ce0c13099","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"jobId":"73d10f9d-6dc7-4f58-b7a4-5eddaa7e070c","level":"info","message":"Assessment job processed successfully","processingTime":"80156ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"55e3bb4a-023d-4595-9a5c-7251b88c8753","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"jobId":"6b1b0675-b503-4470-bdff-94ced0d84811","level":"info","message":"Assessment job processed successfully","processingTime":"80156ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"b48604d3-f173-49be-b0b5-0deae0ba6961","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"jobId":"7b77d10a-95ad-44e4-96db-1866672256dc","level":"info","message":"Assessment job processed successfully","processingTime":"80157ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"941966b0-32bf-4500-92da-551e653bfd4b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"jobId":"bbf89d64-59f2-404d-9aac-4ee262f7dee1","level":"info","message":"Assessment job processed successfully","processingTime":"80157ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"3b35416a-e899-4a3a-8bee-f95b7b7d8dbb","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"jobId":"85721e96-0483-4f08-9f83-c970d004445e","level":"info","message":"Assessment job processed successfully","processingTime":"80158ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"adf9102b-8736-4de9-a9ca-32d39df405ac","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"jobId":"16c3af2e-3e9e-47b8-b28a-a1c494f757da","level":"info","message":"Assessment job processed successfully","processingTime":"80157ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:38","userId":"0f00dfa5-8c26-40ad-a835-b0dda6884cfa","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:48","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:48","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:48","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:18","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:18","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:48","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:52:18","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:30","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:30","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:30","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:30","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:30","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:00","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Analysis result saved successfully","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Analysis result saved to Archive Service","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Analysis result saved successfully","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Analysis result saved to Archive Service","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"error","message":"Failed to update assessment job status","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Assessment job status updated","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"error","message":"Failed to update assessment job status","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Assessment job status updated","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Analysis complete notification sent","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Analysis completion notification sent","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"f8953a42-d308-4c67-9193-7763d9a79d7c","level":"info","message":"Assessment job processed successfully","processingTime":"82255ms","resultId":"ef57a7b4-5799-4f18-af39-ef8e9125197e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"8abebb1d-89c0-4994-932f-6f2d7050ccd7","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Analysis complete notification sent","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Analysis completion notification sent","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"jobId":"92de2cdc-4662-4cc7-8453-f85377980870","level":"info","message":"Assessment job processed successfully","processingTime":"82212ms","resultId":"2a73b6ef-949c-4c13-a3cf-e9d37281e7c4","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f7d55c14-e773-48f8-a956-5d733b78c1b0","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Analysis result saved successfully","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Analysis result saved to Archive Service","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Analysis result saved successfully","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Analysis result saved to Archive Service","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"error","message":"Failed to update assessment job status","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Assessment job status updated","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Analysis complete notification sent","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Analysis completion notification sent","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"jobId":"2e546516-4a67-4daf-8e93-cda1802e929c","level":"info","message":"Assessment job processed successfully","processingTime":"82063ms","resultId":"093ab9c4-c599-4a8d-9152-bbaac11204da","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"ceebf4ca-5e3c-46f2-919b-a4ef034c1a1a","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"error","message":"Failed to update assessment job status","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Assessment job status updated","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Analysis complete notification sent","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Analysis completion notification sent","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"jobId":"e25ac3ef-8fe3-4df5-af82-bb8f049116f3","level":"info","message":"Assessment job processed successfully","processingTime":"81988ms","resultId":"3a4b51ba-f761-4a62-a6c8-35d744e54873","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"20a989da-bfc6-4ae9-9183-131207104e06","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Analysis result saved successfully","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Analysis result saved to Archive Service","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Analysis result saved successfully","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Analysis result saved to Archive Service","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"error","message":"Failed to update assessment job status","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Assessment job status updated","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Analysis complete notification sent","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Analysis completion notification sent","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"jobId":"4b7d4cf7-cb3f-414e-a7e1-5c272b28807b","level":"info","message":"Assessment job processed successfully","processingTime":"82073ms","resultId":"2c7f1694-4e71-437a-a1d3-c8fbd184377b","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"80b96d46-e38f-4b9b-8f86-6ff69fc33590","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"error","message":"Failed to update assessment job status","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Assessment job status updated","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Analysis complete notification sent","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Analysis completion notification sent","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"jobId":"20873262-ad10-4e57-9d5e-d5b9aa3014fe","level":"info","message":"Assessment job processed successfully","processingTime":"82046ms","resultId":"d241572c-4a24-430e-9fe6-bfc1f3beb03d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"9f60764a-a956-4400-b699-861e29513421","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Analysis result saved successfully","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Analysis result saved to Archive Service","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Analysis result saved successfully","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Analysis result saved to Archive Service","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"error","message":"Failed to update assessment job status","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Assessment job status updated","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Analysis complete notification sent","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Analysis completion notification sent","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"jobId":"4dd65694-7740-4436-95b9-bc2f6f1e8557","level":"info","message":"Assessment job processed successfully","processingTime":"82060ms","resultId":"e2c82d20-4227-48d1-9fc8-c486cbcd4297","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"30db9a2e-c5b7-4ef7-a113-0e8eceebd1ea","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"error","message":"Failed to update assessment job status","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Assessment job status updated","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Analysis complete notification sent","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Analysis completion notification sent","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"jobId":"ac556758-595f-4626-9831-67b77b624d02","level":"info","message":"Assessment job processed successfully","processingTime":"82056ms","resultId":"dd6382b2-e16f-402f-94fd-f8235dfb44ec","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"3dd6cbed-33d6-43bf-a3b6-758ee36594c5","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:30","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Analysis result saved successfully","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Analysis result saved to Archive Service","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Analysis result saved successfully","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Analysis result saved to Archive Service","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"error","message":"Failed to update assessment job status","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Assessment job status updated","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Analysis complete notification sent","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Analysis completion notification sent","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"jobId":"7a90e607-d122-4ccf-a31c-ad100c09973a","level":"info","message":"Assessment job processed successfully","processingTime":"82095ms","resultId":"75c9aa06-426b-49e5-97a0-d4a8ccf895b6","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5b52a7e4-f2d3-4057-9c03-db78eee43d84","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"error","message":"Failed to update assessment job status","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Assessment job status updated","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Analysis complete notification sent","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Analysis completion notification sent","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"jobId":"d172f4ce-4a7a-4056-be8f-050b1c9909a4","level":"info","message":"Assessment job processed successfully","processingTime":"82119ms","resultId":"d4468e2c-31bf-41b0-a08f-c604accf34c1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"5da88999-bf62-4e88-b795-261cf4c11329","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:30","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Analysis result saved successfully","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Analysis result saved to Archive Service","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Analysis result saved successfully","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Analysis result saved to Archive Service","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"error","message":"Failed to update assessment job status","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Assessment job status updated","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"error","message":"Failed to update assessment job status","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Assessment job status updated","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Analysis complete notification sent","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Analysis completion notification sent","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"367f091d-d2ec-463e-b4d1-7034dc5ddd99","level":"info","message":"Assessment job processed successfully","processingTime":"82043ms","resultId":"1545f714-228b-4ca2-bdde-3e0164a64101","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"417f5167-d99b-47b8-83ba-4eff1db5e087","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Analysis complete notification sent","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Analysis completion notification sent","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"jobId":"26a165a4-4959-4b07-ad01-6f36701ec364","level":"info","message":"Assessment job processed successfully","processingTime":"82041ms","resultId":"8bdc340a-81f9-492b-a1df-7c5aa6acc503","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c84c463d-7221-4891-ad9b-a31fd5a42afc","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Analysis result saved successfully","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Analysis result saved to Archive Service","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Analysis result saved successfully","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Analysis result saved to Archive Service","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"error","message":"Failed to update assessment job status","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Assessment job status updated","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Analysis complete notification sent","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Analysis completion notification sent","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"jobId":"3641527d-fee6-4cdb-9783-fc2853a4c2c1","level":"info","message":"Assessment job processed successfully","processingTime":"82042ms","resultId":"634d1c6b-e5c1-4091-ac33-5f133a122c6c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"67b2c4d1-8f39-48ee-b879-9b5bf0e2470f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"error","message":"Failed to update assessment job status","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Assessment job status updated","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Analysis complete notification sent","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Analysis completion notification sent","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"jobId":"be308345-e8ad-4b62-8efc-534c7669a828","level":"info","message":"Assessment job processed successfully","processingTime":"82042ms","resultId":"77186715-6283-4c6e-a14c-fa2728a3dfdd","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"26a32dde-ff3d-4e61-9dce-4d696cb80911","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Analysis result saved successfully","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Analysis result saved to Archive Service","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Analysis result saved successfully","resultId":"0afd120e-1050-477b-9636-69630c315f06","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Analysis result saved to Archive Service","resultId":"0afd120e-1050-477b-9636-69630c315f06","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"error","message":"Failed to update assessment job status","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Assessment job status updated","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Analysis complete notification sent","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Analysis completion notification sent","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"jobId":"5d0ac64f-11e0-4004-9f8b-305b55638cfe","level":"info","message":"Assessment job processed successfully","processingTime":"82042ms","resultId":"2fc9a496-14d2-4b95-93a5-33d1d4c19e88","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"dbad6680-b669-4c30-ad8a-60d00eeb04a5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"error","message":"Failed to update assessment job status","resultId":"0afd120e-1050-477b-9636-69630c315f06","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Assessment job status updated","resultId":"0afd120e-1050-477b-9636-69630c315f06","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Analysis complete notification sent","resultId":"0afd120e-1050-477b-9636-69630c315f06","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Analysis completion notification sent","resultId":"0afd120e-1050-477b-9636-69630c315f06","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"jobId":"358edc6c-8cb2-4b76-ac64-f0cb81e8c1a0","level":"info","message":"Assessment job processed successfully","processingTime":"82031ms","resultId":"0afd120e-1050-477b-9636-69630c315f06","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"22fe158c-9f9a-41bd-8158-19017102b025","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:30","userEmail":"<EMAIL>","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","userEmail":"<EMAIL>","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:30","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Analysis result saved successfully","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Analysis result saved to Archive Service","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Analysis result saved successfully","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Analysis result saved to Archive Service","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"error","message":"Failed to update assessment job status","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Assessment job status updated","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Analysis complete notification sent","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Analysis completion notification sent","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"jobId":"049291fc-b311-488c-8fae-398bc9fb5e65","level":"info","message":"Assessment job processed successfully","processingTime":"82068ms","resultId":"347af590-b758-45da-bd23-2c0ecb65749e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"b2f4905a-eab8-478f-9256-40437aecdfe7","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"error","message":"Failed to update assessment job status","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Assessment job status updated","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Analysis complete notification sent","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Analysis completion notification sent","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"jobId":"5333630e-2783-457e-b104-371b44d7efad","level":"info","message":"Assessment job processed successfully","processingTime":"81811ms","resultId":"d3ded056-581d-4854-a97c-bfa2ea3ddc17","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"54c31b95-26a2-4251-a1fa-1d0f4218a79b","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Analysis result saved successfully","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Analysis result saved to Archive Service","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Analysis result saved successfully","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Analysis result saved to Archive Service","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"error","message":"Failed to update assessment job status","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Assessment job status updated","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Analysis complete notification sent","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Analysis completion notification sent","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"jobId":"b6037170-3416-4fb4-a794-25ff9181ea73","level":"info","message":"Assessment job processed successfully","processingTime":"82105ms","resultId":"22013a10-ea4e-4c01-bf1a-0d5c41381dcf","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"38e769d6-511c-422b-ad9a-7d7b2154d55f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"error","message":"Failed to update assessment job status","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Assessment job status updated","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Analysis complete notification sent","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Analysis completion notification sent","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"jobId":"93583c6f-bb81-4a5f-8b85-8c00f68f796c","level":"info","message":"Assessment job processed successfully","processingTime":"82100ms","resultId":"1c34f04e-5ff6-47a3-b31c-aecc5954ef28","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f4cda08d-ae6f-48b3-a050-cd5bbd75673d","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Analysis result saved successfully","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Analysis result saved to Archive Service","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Analysis result saved successfully","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Analysis result saved to Archive Service","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"error","message":"Failed to update assessment job status","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Assessment job status updated","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Analysis complete notification sent","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Analysis completion notification sent","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"jobId":"46b78fc0-771a-41a9-903f-e2207a9bd990","level":"info","message":"Assessment job processed successfully","processingTime":"81980ms","resultId":"961b8287-9ba0-4a6a-94b7-e9bf50979624","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"eb26ec1f-56b7-45f8-9c59-9cc04a10c4aa","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"error","message":"Failed to update assessment job status","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Assessment job status updated","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Analysis complete notification sent","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Analysis completion notification sent","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"jobId":"77fdbbaf-a76f-4089-9e16-6dc2428ff582","level":"info","message":"Assessment job processed successfully","processingTime":"81906ms","resultId":"2e150eb3-df16-4aa4-a508-1c4d7e1ccd35","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"fa406bc6-8624-4b4d-8f7f-229919f3984d","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Analysis result saved successfully","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Analysis result saved to Archive Service","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Analysis result saved successfully","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Analysis result saved to Archive Service","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"error","message":"Failed to update assessment job status","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Assessment job status updated","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Analysis complete notification sent","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Analysis completion notification sent","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"jobId":"41afe96a-ec84-4929-b36f-802915dc0836","level":"info","message":"Assessment job processed successfully","processingTime":"82055ms","resultId":"3ca09deb-26d0-4c96-b20c-9493b370d272","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"637711e7-6282-4a21-bb70-b4aa370afa66","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"error","message":"Failed to update assessment job status","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Assessment job status updated","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Analysis complete notification sent","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Analysis completion notification sent","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"jobId":"23ce0f8e-0bf2-4004-9602-e2b1a68770ef","level":"info","message":"Assessment job processed successfully","processingTime":"81995ms","resultId":"c67ae4bc-9593-4eaf-bd14-cd843bfa0861","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"f88384ab-687b-49b1-87fe-1b7d95e25c72","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Analysis result saved successfully","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Analysis result saved to Archive Service","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Analysis result saved successfully","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Analysis result saved to Archive Service","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"error","message":"Failed to update assessment job status","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Assessment job status updated","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Analysis complete notification sent","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Analysis completion notification sent","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"jobId":"187a1f95-b45c-4248-9a13-99d6bd1b8e90","level":"info","message":"Assessment job processed successfully","processingTime":"82094ms","resultId":"c83ebccb-caa7-4002-8112-780fa0d607d0","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"dd4fd3c5-b612-4018-816a-598b22c76100","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"error","message":"Failed to update assessment job status","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Assessment job status updated","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Analysis complete notification sent","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Analysis completion notification sent","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"jobId":"98df451f-9bfb-4979-9f16-33d17329a325","level":"info","message":"Assessment job processed successfully","processingTime":"81959ms","resultId":"6d8b5de9-04fb-41e8-8d68-7dc7f89117b7","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aeaa9a79-3902-49b5-aad2-4da180dd41ab","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:00","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Analysis result saved successfully","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Analysis result saved to Archive Service","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Analysis result saved successfully","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Analysis result saved to Archive Service","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"error","message":"Failed to update assessment job status","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Assessment job status updated","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"error","message":"Failed to update assessment job status","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Assessment job status updated","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Analysis complete notification sent","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Analysis completion notification sent","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"373f6b1e-c1b8-45f0-b6f5-e397339cf59a","level":"info","message":"Assessment job processed successfully","processingTime":"82096ms","resultId":"35c8b106-6c08-49cd-87cc-f7c9ac9fb22f","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"26d019f7-641a-4494-8631-e3f6786f2694","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Analysis complete notification sent","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Analysis completion notification sent","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"jobId":"643e66b3-8fc3-42d5-975e-960710030b09","level":"info","message":"Assessment job processed successfully","processingTime":"82067ms","resultId":"183288d5-269f-41d6-aece-4bfc50067d91","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"5f82be5d-a54e-4e55-94fb-8bdaf8e1a326","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:00","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:30","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:15:00","version":"1.0.0"}
