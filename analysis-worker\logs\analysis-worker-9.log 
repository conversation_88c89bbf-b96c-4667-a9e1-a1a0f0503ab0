{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","userEmail":"<EMAIL>","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","useMockModel":true,"version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:28","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"archetype":"The Innovative Thinker","careerCount":5,"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Innovative Thinker","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"archetype":"The Methodical Analyst","careerCount":5,"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Methodical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:48","version":"1.0.0","weaknessesCount":3}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:48","useBatch":true,"userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"batched":true,"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"batched":true,"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"batched":true,"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"batched":true,"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"batched":true,"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"batched":true,"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"batched":true,"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"batched":true,"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"batched":true,"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"batched":true,"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:50","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"jobId":"700d33a5-6f17-4fd8-be5a-863b9135b4a9","level":"info","message":"Assessment job processed successfully","processingTime":"82112ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"3cc99fda-8d68-425d-ab45-daf98af24f07","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:50","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"jobId":"71dbfe13-5887-4bef-b65b-1fa362c1bd86","level":"info","message":"Assessment job processed successfully","processingTime":"82112ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"e69e11b1-a514-46ab-8ce3-2ce1aeeadf58","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"jobId":"43e64387-5d45-4642-bc2c-e943646190af","level":"info","message":"Assessment job processed successfully","processingTime":"82111ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"65639bf2-9c7b-422d-bdb4-d60b096ab869","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"jobId":"c641d66b-8c5c-4376-8aee-07660ad9e419","level":"info","message":"Assessment job processed successfully","processingTime":"82107ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7cc8c2b8-c9e9-4676-8d9b-48cb1a479430","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"jobId":"54e51c8a-f171-4d36-b342-a609ed7a00e9","level":"info","message":"Assessment job processed successfully","processingTime":"82113ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"7ce922a8-8e1e-4fc8-86c1-94021cb195e8","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"jobId":"71983015-878e-43b0-a8fb-196efa3aa30c","level":"info","message":"Assessment job processed successfully","processingTime":"82113ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"8ff43c56-eabe-4a6d-b5e1-0c302c444f44","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"jobId":"8a61baa7-eea9-4c0a-92ac-de0d5d88870d","level":"info","message":"Assessment job processed successfully","processingTime":"82113ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d475f0f8-b6f3-471f-bcb6-b6e4ab446365","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"jobId":"8b584ba8-e3ad-45be-af5f-0e26831a474b","level":"info","message":"Assessment job processed successfully","processingTime":"82115ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"4f0a8cb1-425e-4473-bbca-bde065d94d16","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"jobId":"9f5b04d4-6ad8-48b9-b7bc-5efdc8889b6d","level":"info","message":"Assessment job processed successfully","processingTime":"82116ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"d523fa27-fcee-4ea1-ade2-d1f6add68ef2","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"jobId":"ec875bfa-689e-4eb5-b76e-6bf75f7a5b3e","level":"info","message":"Assessment job processed successfully","processingTime":"82116ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:50","userId":"70f51d3c-9fd1-49e4-976e-c3a00881ba24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:58","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:58","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:28","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:28","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:58","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:40","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:40","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:40","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:40","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:40","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:40","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:10","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Analysis result saved successfully","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Analysis result saved to Archive Service","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Analysis result saved successfully","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Analysis result saved to Archive Service","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"error","message":"Failed to update assessment job status","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Assessment job status updated","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"error","message":"Failed to update assessment job status","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Assessment job status updated","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Analysis complete notification sent","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Analysis completion notification sent","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"85e2c462-258f-44e1-8a15-b6966d555662","level":"info","message":"Assessment job processed successfully","processingTime":"82206ms","resultId":"52e9c317-550f-41ab-9a4d-8edec4ff0a7d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"05c3fe6c-5553-42dd-96cf-9eb8610a300b","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Analysis complete notification sent","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Analysis completion notification sent","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"jobId":"bf8a82a2-2cb7-485f-bcaf-8f1649b837e8","level":"info","message":"Assessment job processed successfully","processingTime":"82123ms","resultId":"60e063dc-6bae-4f62-a797-6b06b6ef6ba8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"e48f584a-54d7-4200-9c34-066bc42c0b53","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Analysis result saved successfully","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Analysis result saved to Archive Service","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Analysis result saved successfully","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Analysis result saved to Archive Service","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"error","message":"Failed to update assessment job status","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Assessment job status updated","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Analysis complete notification sent","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Analysis completion notification sent","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"jobId":"f930ba58-cfe4-4fd7-a3f1-4d8ed91977a0","level":"info","message":"Assessment job processed successfully","processingTime":"81989ms","resultId":"67617e4c-238a-4927-909f-a1d19ef6ea29","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"503c5445-7725-482c-90fe-f824cb446fde","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"error","message":"Failed to update assessment job status","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Assessment job status updated","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Analysis complete notification sent","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Analysis completion notification sent","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"jobId":"f227a9f7-42c8-4e99-8bac-62fd44d9a8c2","level":"info","message":"Assessment job processed successfully","processingTime":"81988ms","resultId":"57324e39-d202-4d52-8f4b-490f2db0739d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"f355af51-725f-4026-a74e-750763dd7b3f","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Analysis result saved successfully","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Analysis result saved to Archive Service","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Analysis result saved successfully","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Analysis result saved to Archive Service","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"error","message":"Failed to update assessment job status","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Assessment job status updated","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Analysis complete notification sent","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Analysis completion notification sent","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"jobId":"10de1067-c3b7-4fed-9a9e-43602c10223c","level":"info","message":"Assessment job processed successfully","processingTime":"82079ms","resultId":"b565082f-edd2-4ed6-aa3b-d99d0fe2cd28","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"d96ed502-4744-4a54-a861-bc1c1936b045","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a8047376-033e-443b-9270-5223123af930","level":"error","message":"Failed to update assessment job status","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Assessment job status updated","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Analysis complete notification sent","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Analysis completion notification sent","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"jobId":"a8047376-033e-443b-9270-5223123af930","level":"info","message":"Assessment job processed successfully","processingTime":"82011ms","resultId":"1adcd354-96c7-4706-8bda-e2dc8764db0d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"0322e93d-ef30-4eb9-8b0f-d8cdd55a962b","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Analysis result saved successfully","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Analysis result saved to Archive Service","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Analysis result saved successfully","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Analysis result saved to Archive Service","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"error","message":"Failed to update assessment job status","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Assessment job status updated","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Analysis complete notification sent","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Analysis completion notification sent","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"jobId":"1a9a5362-9c17-4275-b1ab-2f42e30d2ce6","level":"info","message":"Assessment job processed successfully","processingTime":"82046ms","resultId":"cc0967c2-c7f0-4a03-a8ab-11739697440e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"697fb9f6-b07a-48be-8f38-fb8f64b77347","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"error","message":"Failed to update assessment job status","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Assessment job status updated","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Analysis complete notification sent","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Analysis completion notification sent","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"jobId":"1d8b5259-9a2e-4b85-ad03-95af837fa1d3","level":"info","message":"Assessment job processed successfully","processingTime":"81991ms","resultId":"6f7a0512-8d8d-4b82-aa3f-2a748a6bbcc2","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"f8040da4-c072-4bd1-ac54-28759f6c2b8e","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Analysis result saved successfully","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Analysis result saved to Archive Service","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Analysis result saved successfully","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Analysis result saved to Archive Service","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"error","message":"Failed to update assessment job status","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Assessment job status updated","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Analysis complete notification sent","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Analysis completion notification sent","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"jobId":"4e864eea-48d0-4c29-8dad-77f8840633a2","level":"info","message":"Assessment job processed successfully","processingTime":"82121ms","resultId":"577d3b95-8db7-494d-9da4-e921d739bbf4","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"ee74ab65-38dc-4e6f-8687-9b158c613100","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"error","message":"Failed to update assessment job status","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Assessment job status updated","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Analysis complete notification sent","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Analysis completion notification sent","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"jobId":"372c734e-cf57-49a3-b992-cecd2f8574ff","level":"info","message":"Assessment job processed successfully","processingTime":"82069ms","resultId":"b3bf54ae-92c8-4194-b6e8-3355e066022f","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"32a459d0-397f-46ea-bff0-6ed45b05b158","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:40","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Analysis result saved successfully","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Analysis result saved to Archive Service","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Analysis result saved successfully","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Analysis result saved to Archive Service","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"error","message":"Failed to update assessment job status","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Assessment job status updated","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Analysis complete notification sent","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Analysis completion notification sent","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"jobId":"3911dc9f-cfc9-48a1-adb4-440d85ad8aef","level":"info","message":"Assessment job processed successfully","processingTime":"82049ms","resultId":"fafd71e7-536b-46b2-a525-64fae60bf50d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"792dbc62-40e4-493f-91f7-f977388f3ad1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"error","message":"Failed to update assessment job status","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Assessment job status updated","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Analysis complete notification sent","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Analysis completion notification sent","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"jobId":"65cf7d65-7cfe-446d-82b9-7d717c6a20ec","level":"info","message":"Assessment job processed successfully","processingTime":"82053ms","resultId":"339a1ee7-1422-445b-a9f1-e866390672e3","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"c41233e0-57b3-4331-bd98-71077ccfa7f0","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Analysis result saved successfully","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Analysis result saved to Archive Service","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Analysis result saved successfully","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Analysis result saved to Archive Service","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"error","message":"Failed to update assessment job status","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Assessment job status updated","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Analysis complete notification sent","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Analysis completion notification sent","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"jobId":"48687001-8684-40a8-9cde-6d88d9ebef82","level":"info","message":"Assessment job processed successfully","processingTime":"82031ms","resultId":"bcaab6ef-fa90-4dcb-9f10-ee0b2eeef918","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"2fcdf5d9-5ead-4e1a-82f1-6b80d8352432","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"error","message":"Failed to update assessment job status","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:45","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Assessment job status updated","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Analysis complete notification sent","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Analysis completion notification sent","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"jobId":"5c0eb18c-f6f1-4db9-9442-48bdea449e5f","level":"info","message":"Assessment job processed successfully","processingTime":"82044ms","resultId":"63bc57d8-9181-4ce0-b83b-cffebd309081","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:45","userId":"ce1427a6-613f-414a-8410-bb0080ae7ad0","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","userEmail":"<EMAIL>","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:40","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Analysis result saved successfully","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Analysis result saved to Archive Service","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Analysis result saved successfully","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Analysis result saved to Archive Service","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"error","message":"Failed to update assessment job status","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Assessment job status updated","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Analysis complete notification sent","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Analysis completion notification sent","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"jobId":"25d76f36-4f59-4208-b531-14333a3574fe","level":"info","message":"Assessment job processed successfully","processingTime":"82077ms","resultId":"3de5140c-f69d-4dd3-af6a-dfc837427444","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"c4549c0a-d1ed-438f-8d4f-d5a3129d0489","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"error","message":"Failed to update assessment job status","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Assessment job status updated","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Analysis complete notification sent","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Analysis completion notification sent","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"jobId":"7a2207c2-9d57-4273-86a0-a300251e8238","level":"info","message":"Assessment job processed successfully","processingTime":"82015ms","resultId":"2073c336-101a-4461-9ebf-02dfef310e01","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"fd2924ab-07dc-478a-a47f-27416887d95e","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userEmail":"<EMAIL>","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","useMockModel":true,"version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:46","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Analysis result saved successfully","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Analysis result saved to Archive Service","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Analysis result saved successfully","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Analysis result saved to Archive Service","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"error","message":"Failed to update assessment job status","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Assessment job status updated","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Analysis complete notification sent","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Analysis completion notification sent","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"jobId":"98337844-f247-452a-b1c3-8e914b9fb038","level":"info","message":"Assessment job processed successfully","processingTime":"82170ms","resultId":"8bcc9f4f-59ce-4887-b511-fcf480095a4e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"4a8632d6-052a-40fb-9c73-6680433a8ab1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"error","message":"Failed to update assessment job status","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Assessment job status updated","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Analysis complete notification sent","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Analysis completion notification sent","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"jobId":"fe9674cd-097f-4e11-9e01-a6d63e6af3be","level":"info","message":"Assessment job processed successfully","processingTime":"82082ms","resultId":"fa49d4eb-03b5-4654-9017-9502fe1a6cf9","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f676ea07-4689-4e28-8e3f-47389aed2937","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Analysis result saved successfully","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Analysis result saved to Archive Service","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Analysis result saved successfully","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Analysis result saved to Archive Service","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"error","message":"Failed to update assessment job status","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Assessment job status updated","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Analysis complete notification sent","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Analysis completion notification sent","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"jobId":"5eaa3968-12cd-48d3-b83b-a8fd2a0e2e30","level":"info","message":"Assessment job processed successfully","processingTime":"82066ms","resultId":"14edc3d8-796b-4b88-b75b-8f62fcc04db6","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"9e52dea6-e9be-4e9e-bd29-bc8df34b81b7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"error","message":"Failed to update assessment job status","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Assessment job status updated","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Analysis complete notification sent","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Analysis completion notification sent","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"jobId":"0d72acf6-c84a-45d0-948c-2b3716524861","level":"info","message":"Assessment job processed successfully","processingTime":"81969ms","resultId":"542832be-fe7f-4bd6-94c9-57dcc21f9a3b","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"84d7dbaf-2c56-41dc-8b40-6f9dfe47623f","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Analysis result saved successfully","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Analysis result saved to Archive Service","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Analysis result saved successfully","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Analysis result saved to Archive Service","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"error","message":"Failed to update assessment job status","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Assessment job status updated","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Analysis complete notification sent","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Analysis completion notification sent","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"jobId":"2b4a08d5-044f-4cd4-ab0c-a17aa04cff83","level":"info","message":"Assessment job processed successfully","processingTime":"82075ms","resultId":"b952a2fc-c54e-4782-add1-c207364bb66f","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"44248f24-58e8-4085-a3db-97382f3e17c6","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"error","message":"Failed to update assessment job status","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Assessment job status updated","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Analysis complete notification sent","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Analysis completion notification sent","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"jobId":"3d2be311-f0d8-457b-8c81-455d902b266f","level":"info","message":"Assessment job processed successfully","processingTime":"82072ms","resultId":"ff37436b-fe49-423d-9a40-1f58e6ebe618","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"47d14314-557f-4b5d-b227-9fee878f91f3","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Analysis result saved successfully","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Analysis result saved to Archive Service","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Analysis result saved successfully","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Analysis result saved to Archive Service","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"error","message":"Failed to update assessment job status","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Assessment job status updated","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Analysis complete notification sent","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Analysis completion notification sent","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"jobId":"8d51f2dc-ca7b-4da3-ae65-64c9cba35014","level":"info","message":"Assessment job processed successfully","processingTime":"82100ms","resultId":"6f783c47-8db0-4a94-8319-178e0c8cb5dd","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"ec3a4bbf-3a29-4c96-941e-02553a0c56c8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"error","message":"Failed to update assessment job status","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Assessment job status updated","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Analysis complete notification sent","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Analysis completion notification sent","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"jobId":"af094b57-df3d-45a6-bb0e-ed33dc0d37c4","level":"info","message":"Assessment job processed successfully","processingTime":"82097ms","resultId":"76b16398-ebf0-4524-856d-b9967a4b94fb","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"cfc83030-16b7-4064-8142-a277a8260dae","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:40","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:06","version":"1.0.0","weaknessesCount":3}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:02:06","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Analysis result saved successfully","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Analysis result saved to Archive Service","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Analysis result saved successfully","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Analysis result saved to Archive Service","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"error","message":"Failed to update assessment job status","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Assessment job status updated","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"error","message":"Failed to update assessment job status","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Assessment job status updated","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Analysis complete notification sent","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Analysis completion notification sent","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"469b6584-2c30-468e-a290-b2fe71088beb","level":"info","message":"Assessment job processed successfully","processingTime":"82105ms","resultId":"933b5591-61d2-45a2-828f-4469abbd3272","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c237084d-34a0-4abb-b215-6502157239fe","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Analysis complete notification sent","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Analysis completion notification sent","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"jobId":"8dcc71ac-32a2-45f3-ad70-201ee4e1b7a4","level":"info","message":"Assessment job processed successfully","processingTime":"82099ms","resultId":"f68e6121-190c-4876-a1df-86e80c9e3dab","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"c71a7544-9cd7-4ac2-bea7-0f9ce2193c21","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:40","version":"1.0.0"}
