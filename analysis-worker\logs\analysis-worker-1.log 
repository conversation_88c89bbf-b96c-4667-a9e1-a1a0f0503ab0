{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Checking Archive Service health...","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"level":"info","message":"Archive Service is healthy","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","userEmail":"<EMAIL>","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","useMockModel":true,"version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:43:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:43:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:12","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:44:32","version":"1.0.0","weaknessesCount":3}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:44:32","useBatch":true,"userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"batched":true,"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"batched":true,"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"batched":true,"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"batched":true,"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"batched":true,"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"batched":true,"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"batched":true,"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"batched":true,"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"batched":true,"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"batched":true,"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Analysis result saved successfully","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:44:34","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Analysis result saved to Archive Service","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"jobId":"a0980cd4-0a8a-496c-99df-1715b7af6b5a","level":"info","message":"Assessment job processed successfully","processingTime":"82180ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"7e49b54d-a006-48a1-957c-5447bb050049","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"error","message":"Failed to update assessment job status","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Assessment job status updated","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"jobId":"1a7fb1e1-0d20-4690-861f-0554f45b4ed0","level":"info","message":"Assessment job processed successfully","processingTime":"82180ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"45dff254-ba35-47c2-97aa-3b7525590ab4","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userEmail":"<EMAIL>","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userEmail":"<EMAIL>","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","useMockModel":true,"version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"jobId":"2f2dac7f-bf0f-4489-b51b-a48183a602a1","level":"info","message":"Assessment job processed successfully","processingTime":"82181ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5f5f81e3-0705-4ff6-a08d-34b53c16378b","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"jobId":"fd1744e6-8a0f-42b9-a7b7-5e911b00270f","level":"info","message":"Assessment job processed successfully","processingTime":"82182ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"cbb7c30f-842c-4c4b-acd6-7e2211d3d320","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"jobId":"708d1d88-e566-4be2-94c0-3f9dc9b4005c","level":"info","message":"Assessment job processed successfully","processingTime":"82185ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"06181d17-a349-4ac8-b272-29c588c50b20","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"jobId":"540d82b3-5d7d-462b-a90b-df74fc9c5dce","level":"info","message":"Assessment job processed successfully","processingTime":"82187ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"d4afbac5-aad5-4eda-8680-4a2842e1b010","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"jobId":"90ed3d85-ab30-476a-a4fa-d5776731a69f","level":"info","message":"Assessment job processed successfully","processingTime":"82189ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"f3e2da18-68e9-4a3b-810f-c5490b512fb8","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"jobId":"ba2eb3d5-d8e5-4d8d-9f03-07b2e1936231","level":"info","message":"Assessment job processed successfully","processingTime":"82187ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"5ea78b33-eeba-42f7-bddc-479942debb28","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"jobId":"81ae93d7-cae8-42e7-9054-85441bd0b18e","level":"info","message":"Assessment job processed successfully","processingTime":"82192ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"c478bc43-d9be-4003-a61d-81483a472ad7","version":"1.0.0"}
{"error":"Request failed with status code 400","jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Analysis completion notification sent","service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"jobId":"94d4ff10-2a7d-4c26-b726-f005a82504f8","level":"info","message":"Assessment job processed successfully","processingTime":"82192ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:44:34","userId":"1123c6a1-63fc-4c65-8ba3-31b62ecb7485","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:44:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:45:42","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:45:54","version":"1.0.0","weaknessesCount":3}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:45:54","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:45:54","useBatch":true,"userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"batched":true,"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Analysis result saved successfully","resultId":"122231a6-87a4-4130-b908-c145f6b17908","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:45:56","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Analysis result saved to Archive Service","resultId":"122231a6-87a4-4130-b908-c145f6b17908","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"error","message":"Failed to update assessment job status","resultId":"122231a6-87a4-4130-b908-c145f6b17908","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:45:56","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Assessment job status updated","resultId":"122231a6-87a4-4130-b908-c145f6b17908","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Analysis complete notification sent","resultId":"122231a6-87a4-4130-b908-c145f6b17908","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Analysis completion notification sent","resultId":"122231a6-87a4-4130-b908-c145f6b17908","service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"jobId":"cffe2d7e-d17b-4cbb-9a9b-9a32ec90fcb3","level":"info","message":"Assessment job processed successfully","processingTime":"82028ms","resultId":"122231a6-87a4-4130-b908-c145f6b17908","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:45:56","userId":"d7944834-97be-4e82-8b76-cdaa2c2affe8","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:46:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:46:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:46:42","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:12","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:47:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:47:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:47:42","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:48:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:48:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:48:42","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:49:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:49:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:49:42","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:50:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:50:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:50:42","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:51:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:51:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:51:42","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-19 04:52:12","url":"/health","version":"1.0.0"}
{"error":"","level":"error","message":"Archive Service health check failed","service":"analysis-worker","timestamp":"2025-07-19 04:52:12","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:52:12","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-19 04:53:24","version":"1.0.0","workerConcurrency":"10 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-19 04:53:24","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:24","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-19 04:53:24","version":"1.0.0"}
{"concurrency":10,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-19 04:53:24","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-19 04:53:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:53:54","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","userEmail":"<EMAIL>","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","useMockModel":true,"version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:53:58","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","userEmail":"<EMAIL>","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","useMockModel":true,"version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:00","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","userEmail":"<EMAIL>","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","useMockModel":true,"version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:04","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","userEmail":"<EMAIL>","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","useMockModel":true,"version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:07","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","userEmail":"<EMAIL>","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","useMockModel":true,"version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:54:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:54:54","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:18","version":"1.0.0","weaknessesCount":3}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:18","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Analysis result saved successfully","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Analysis result saved to Archive Service","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Analysis result saved successfully","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:20","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Analysis result saved to Archive Service","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"error","message":"Failed to update assessment job status","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Assessment job status updated","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"error","message":"Failed to update assessment job status","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Assessment job status updated","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Analysis complete notification sent","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Analysis completion notification sent","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"27dd012b-fe6d-494f-a1e4-7984770afb59","level":"info","message":"Assessment job processed successfully","processingTime":"82191ms","resultId":"a2faf77e-5cb7-48a5-94df-50b8876ec173","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"c8d22b3f-2aeb-41d5-b39d-617830cde886","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Analysis complete notification sent","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Analysis completion notification sent","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"jobId":"69df6a41-27aa-4070-83b0-ec0a924b96b5","level":"info","message":"Assessment job processed successfully","processingTime":"82174ms","resultId":"3decde9b-1553-4c2a-aa67-fb4fa9509ee8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"9c73dc38-2e1c-47d8-860e-b658008f4293","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userEmail":"<EMAIL>","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","useMockModel":true,"version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:55:20","version":"1.0.0","weaknessesCount":3}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:20","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Analysis result saved successfully","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Analysis result saved to Archive Service","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Analysis result saved successfully","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:22","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Analysis result saved to Archive Service","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"error","message":"Failed to update assessment job status","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Assessment job status updated","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"error","message":"Failed to update assessment job status","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Assessment job status updated","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Analysis complete notification sent","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Analysis completion notification sent","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"da364c6e-2cf8-4ae2-9d48-594c1e578c13","level":"info","message":"Assessment job processed successfully","processingTime":"82064ms","resultId":"6f82029d-f036-4c51-9fec-a3ca74c9a12d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"53a37c6f-40b5-417a-b065-265e0c5ce564","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Analysis complete notification sent","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Analysis completion notification sent","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"jobId":"a7e58518-f9f5-41d4-b193-4479d5601758","level":"info","message":"Assessment job processed successfully","processingTime":"81965ms","resultId":"f52f6f97-659c-44e8-961a-f9675f28a7a8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userId":"7425795f-811f-4092-b29b-004a1932cebd","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","userEmail":"<EMAIL>","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","useMockModel":true,"version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:22","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:24","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:24","version":"1.0.0","weaknessesCount":3}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:24","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Analysis result saved successfully","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Analysis result saved to Archive Service","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"error","message":"Failed to update assessment job status","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Assessment job status updated","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Analysis result saved successfully","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:26","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Analysis result saved to Archive Service","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"error","message":"Failed to update assessment job status","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Assessment job status updated","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Analysis complete notification sent","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Analysis completion notification sent","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"6a013af0-8be0-44f3-8c35-24f76e0452a4","level":"info","message":"Assessment job processed successfully","processingTime":"82070ms","resultId":"2a43f6eb-2124-4f11-b5be-5c9f24932748","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"e4d81cc3-7581-4542-b142-cce59bb42ed7","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Analysis complete notification sent","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Analysis completion notification sent","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"jobId":"9fab2b5d-85eb-4441-8043-2c81ee95f821","level":"info","message":"Assessment job processed successfully","processingTime":"82019ms","resultId":"fbf374f2-7974-4f8a-a490-2de07853b2a8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userId":"b9c6ec09-778a-49d3-93bc-c2fd3d40084f","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","userEmail":"<EMAIL>","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","useMockModel":true,"version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:55:26","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:27","version":"1.0.0","weaknessesCount":3}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:27","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Analysis result saved successfully","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Analysis result saved to Archive Service","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Analysis result saved successfully","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:29","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Analysis result saved to Archive Service","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"error","message":"Failed to update assessment job status","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Assessment job status updated","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Analysis complete notification sent","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Analysis completion notification sent","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"jobId":"ee2689a4-8fc1-4715-95cc-12e5d0ae419e","level":"info","message":"Assessment job processed successfully","processingTime":"82049ms","resultId":"ab561718-4fb5-4221-8c98-78e56b1da24c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"1fe0d616-7196-43d8-951b-d2eb5fd3a574","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"error","message":"Failed to update assessment job status","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:29","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Assessment job status updated","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Analysis complete notification sent","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Analysis completion notification sent","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"jobId":"26d42ff8-b607-4369-8d8b-70e7e80921c7","level":"info","message":"Assessment job processed successfully","processingTime":"82052ms","resultId":"bbd1b454-d60e-4dc3-ae9b-ad6587cad508","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:29","userId":"37f9be23-e6e9-4a08-98e1-6756d82e66c2","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:55:30","version":"1.0.0","weaknessesCount":3}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 04:55:30","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Analysis result saved successfully","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Analysis result saved to Archive Service","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"error","message":"Failed to update assessment job status","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Assessment job status updated","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Analysis result saved successfully","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:55:32","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Analysis result saved to Archive Service","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Analysis complete notification sent","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Analysis completion notification sent","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"jobId":"02e3657c-242d-45ce-b17e-9c49257ad760","level":"info","message":"Assessment job processed successfully","processingTime":"82082ms","resultId":"90ec904b-1218-42b5-b4ab-d53dac289ef6","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"c72d4655-2e84-46c8-b53d-63490490fb99","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"error","message":"Failed to update assessment job status","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:55:32","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Assessment job status updated","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Analysis complete notification sent","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Analysis completion notification sent","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"jobId":"737bdeb6-97a3-450e-a4a3-9db4d5b1e976","level":"info","message":"Assessment job processed successfully","processingTime":"82094ms","resultId":"afed0481-4ee6-4986-97cb-b31c3a2d2e63","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:55:32","userId":"e6209469-3440-497f-a3bc-8fe92b62da2c","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:55:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:24","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":3,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:40","version":"1.0.0","weaknessesCount":3}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:40","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Analysis result saved successfully","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Analysis result saved to Archive Service","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Analysis result saved successfully","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:42","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Analysis result saved to Archive Service","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"error","message":"Failed to update assessment job status","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Assessment job status updated","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"error","message":"Failed to update assessment job status","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:42","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Assessment job status updated","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Analysis complete notification sent","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Analysis completion notification sent","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"661a6e63-012c-4c01-a527-ca10f0ebdd0a","level":"info","message":"Assessment job processed successfully","processingTime":"82079ms","resultId":"4d92e99c-3d52-4339-91c2-91889ce92ffa","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4264824f-fa72-47ce-9cff-edc12ddbb238","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Analysis complete notification sent","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Analysis completion notification sent","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"jobId":"b1394f6a-008f-4d85-aa8b-f455de1f93cb","level":"info","message":"Assessment job processed successfully","processingTime":"82079ms","resultId":"a260d348-d67a-49a9-9e1c-0afcebe4a4dc","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"df1065a5-eeab-485b-af47-1eb7f2b3ef7f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:42","version":"1.0.0","weaknessesCount":3}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:42","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Analysis result saved successfully","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Analysis result saved to Archive Service","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Analysis result saved successfully","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:44","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Analysis result saved to Archive Service","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"error","message":"Failed to update assessment job status","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Assessment job status updated","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Analysis complete notification sent","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Analysis completion notification sent","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"jobId":"ec036cad-7789-4180-aca0-2553ffcf98d4","level":"info","message":"Assessment job processed successfully","processingTime":"82051ms","resultId":"f7287441-5463-4991-9a8a-ab01e00b142e","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"4e795a4f-f338-4f44-a54e-76b83eae70b8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"error","message":"Failed to update assessment job status","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:44","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Assessment job status updated","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Analysis complete notification sent","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Analysis completion notification sent","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"jobId":"337279d3-41f1-429f-ac3d-53f820a900c0","level":"info","message":"Assessment job processed successfully","processingTime":"82054ms","resultId":"ddad71ae-4d35-42d2-93f5-1f07fddd0575","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:44","userId":"99e96b3f-76d8-4d3a-a7ea-6a754f5e97e7","version":"1.0.0"}
{"archetype":"The Innovative Thinker","careerCount":5,"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Innovative Thinker","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 04:56:46","version":"1.0.0","weaknessesCount":3}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 04:56:46","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Analysis result saved successfully","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Analysis result saved to Archive Service","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Analysis result saved successfully","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","service":"analysis-worker","status":201,"timestamp":"2025-07-19 04:56:48","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Analysis result saved to Archive Service","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"error","message":"Failed to update assessment job status","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Assessment job status updated","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Analysis complete notification sent","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Analysis completion notification sent","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"jobId":"95c43f15-0d90-4714-89af-6a6ad7fa7689","level":"info","message":"Assessment job processed successfully","processingTime":"82045ms","resultId":"f4364216-6998-4c4a-8457-d8b0ed230687","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"6b8af277-e726-417e-92ef-7deb93d3be7f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"error","message":"Failed to update assessment job status","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 04:56:48","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Assessment job status updated","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Analysis complete notification sent","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Analysis completion notification sent","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"jobId":"06a73c07-523f-4251-8be0-3c9201ec7f99","level":"info","message":"Assessment job processed successfully","processingTime":"82046ms","resultId":"ca14f6ad-d5be-4ad3-a3e6-94a943212daa","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:56:48","userId":"936012cb-f91c-46e8-a152-248c54e51acc","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:56:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:57:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:58:54","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","userEmail":"<EMAIL>","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","useMockModel":true,"version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:23","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","userEmail":"<EMAIL>","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","useMockModel":true,"version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:24","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","userEmail":"<EMAIL>","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","useMockModel":true,"version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:27","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","userEmail":"<EMAIL>","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","useMockModel":true,"version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:29","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:30","userEmail":"<EMAIL>","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","userEmail":"<EMAIL>","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","useMockModel":true,"version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:30","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","userEmail":"<EMAIL>","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","useMockModel":true,"version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:33","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","userEmail":"<EMAIL>","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","useMockModel":true,"version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 04:59:36","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 04:59:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:24","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:43","version":"1.0.0","weaknessesCount":3}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:00:43","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:44","version":"1.0.0","weaknessesCount":3}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:44","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Analysis result saved successfully","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Analysis result saved to Archive Service","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Analysis result saved successfully","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:45","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Analysis result saved to Archive Service","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"error","message":"Failed to update assessment job status","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Assessment job status updated","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"error","message":"Failed to update assessment job status","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Assessment job status updated","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Analysis complete notification sent","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Analysis completion notification sent","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"39e04f96-4e83-4ae1-ae7d-f27b685656b5","level":"info","message":"Assessment job processed successfully","processingTime":"82035ms","resultId":"995ca57a-6d52-40b5-b2fd-b658df893642","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"bf507e14-5db3-4c1a-98e5-01286eeca221","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Analysis complete notification sent","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Analysis completion notification sent","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"jobId":"bc959b61-2f96-451b-9858-8e6f1e9d4b45","level":"info","message":"Assessment job processed successfully","processingTime":"81960ms","resultId":"60d9e808-caf4-4cda-a6d0-0e43d3e8c505","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userId":"33382c2f-b750-45f2-8441-d95f8e9b3ae3","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","useMockModel":true,"version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"isRetryAttempt":false,"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Processing assessment job","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Starting assessment processing","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","userEmail":"<EMAIL>","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Generating persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Using mock AI model - returning mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","useMockModel":true,"version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Generating mock persona profile","service":"analysis-worker","timestamp":"2025-07-19 05:00:45","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:00:47","version":"1.0.0","weaknessesCount":3}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:47","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Analysis result saved successfully","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Analysis result saved to Archive Service","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Analysis result saved successfully","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:49","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Analysis result saved to Archive Service","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"error","message":"Failed to update assessment job status","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Assessment job status updated","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Analysis complete notification sent","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Analysis completion notification sent","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"jobId":"42eefc5c-996e-4606-aa90-d4b3372efb04","level":"info","message":"Assessment job processed successfully","processingTime":"82091ms","resultId":"c8456793-5dcb-4bfa-8f71-14bcab541aca","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"b3216923-c8e5-454c-9f9b-d65acda3d995","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"error","message":"Failed to update assessment job status","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:49","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Assessment job status updated","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Analysis complete notification sent","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Analysis completion notification sent","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"jobId":"4ddfd6b9-8f2e-4ece-aa28-b8b0c35239db","level":"info","message":"Assessment job processed successfully","processingTime":"82089ms","resultId":"dc912dc3-06d3-43b6-8f3e-d7b320c3d97d","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"a1593a48-5e9f-4e4c-9432-a881460b18b1","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:49","version":"1.0.0","weaknessesCount":3}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:49","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:50","version":"1.0.0","weaknessesCount":3}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:50","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Analysis result saved successfully","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Analysis result saved to Archive Service","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Analysis result saved successfully","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:51","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Analysis result saved to Archive Service","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"error","message":"Failed to update assessment job status","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Assessment job status updated","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"error","message":"Failed to update assessment job status","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:51","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Assessment job status updated","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Analysis complete notification sent","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Analysis completion notification sent","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"3b831d19-5bdc-4da1-b040-81746d93971a","level":"info","message":"Assessment job processed successfully","processingTime":"81958ms","resultId":"1fbe7416-6087-44fd-8e7b-0a2f6d574d12","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"f9ceb717-aa59-4447-91a2-d728e185919e","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Analysis complete notification sent","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Analysis completion notification sent","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"jobId":"5153b350-0976-467a-8ba6-288b2018fb47","level":"info","message":"Assessment job processed successfully","processingTime":"81875ms","resultId":"8944f0c9-f801-4657-9999-9fc31e0bbdc1","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:51","userId":"d817ceea-0a91-4d10-961f-20c5ea8295b8","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"archetype":"The Technical Problem Solver","careerCount":5,"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:53","version":"1.0.0","weaknessesCount":3}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Technical Problem Solver","service":"analysis-worker","timestamp":"2025-07-19 05:00:53","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:00:54","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Analysis result saved successfully","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Analysis result saved to Archive Service","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Analysis result saved successfully","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:55","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Analysis result saved to Archive Service","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"error","message":"Failed to update assessment job status","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Assessment job status updated","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"error","message":"Failed to update assessment job status","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:55","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Assessment job status updated","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Analysis complete notification sent","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Analysis completion notification sent","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"123fdfc8-1b8b-4c9a-a4fb-61f3fcc29969","level":"info","message":"Assessment job processed successfully","processingTime":"82043ms","resultId":"9c04d28f-0c2d-42eb-885c-24388bf44011","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"10e9e31c-d48b-477d-bd19-233129b9b12f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Analysis complete notification sent","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Analysis completion notification sent","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"jobId":"2a03207b-5105-4d42-930c-697bcca0558c","level":"info","message":"Assessment job processed successfully","processingTime":"81964ms","resultId":"66e80017-e0f5-4d36-98d0-721c82ee3775","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:55","userId":"b363da8e-53f7-4a66-93a1-e0469e14fa9f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"archetype":"The Balanced Professional","careerCount":5,"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:00:56","version":"1.0.0","weaknessesCount":3}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Balanced Professional","service":"analysis-worker","timestamp":"2025-07-19 05:00:56","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Analysis result saved successfully","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Analysis result saved to Archive Service","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Analysis result saved successfully","resultId":"dc182638-703b-4f3b-be78-c6792801550c","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:00:58","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Analysis result saved to Archive Service","resultId":"dc182638-703b-4f3b-be78-c6792801550c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"error","message":"Failed to update assessment job status","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Assessment job status updated","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Analysis complete notification sent","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Analysis completion notification sent","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"jobId":"85b99320-563c-4125-8bf6-34148ccfffd2","level":"info","message":"Assessment job processed successfully","processingTime":"82060ms","resultId":"c1c8c4ee-4baa-43be-870f-8f8f0a05ae65","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"f9f917a4-fcd5-47c0-84b8-c16eb3f8cb7f","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"error","message":"Failed to update assessment job status","resultId":"dc182638-703b-4f3b-be78-c6792801550c","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:00:58","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Assessment job status updated","resultId":"dc182638-703b-4f3b-be78-c6792801550c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Analysis complete notification sent","resultId":"dc182638-703b-4f3b-be78-c6792801550c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Analysis completion notification sent","resultId":"dc182638-703b-4f3b-be78-c6792801550c","service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"jobId":"e0df657b-3933-4a21-867b-3e72ba8587b8","level":"info","message":"Assessment job processed successfully","processingTime":"81991ms","resultId":"dc182638-703b-4f3b-be78-c6792801550c","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:00:58","userId":"aedf2e8e-7ce3-42aa-a1f5-9ab0479daee5","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:01:54","version":"1.0.0"}
{"archetype":"The Practical Analyst","careerCount":5,"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":5,"timestamp":"2025-07-19 05:02:05","version":"1.0.0","weaknessesCount":3}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:05","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Practical Analyst","service":"analysis-worker","timestamp":"2025-07-19 05:02:05","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"archetype":"The Creative Researcher","careerCount":5,"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Mock persona profile generated successfully","service":"analysis-worker","strengthsCount":4,"timestamp":"2025-07-19 05:02:05","version":"1.0.0","weaknessesCount":3}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Persona profile generated successfully","service":"analysis-worker","timestamp":"2025-07-19 05:02:05","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Saving analysis result to Archive Service","profileArchetype":"The Creative Researcher","service":"analysis-worker","timestamp":"2025-07-19 05:02:05","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Analysis result saved successfully","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Analysis result saved to Archive Service","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Analysis result saved successfully","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","service":"analysis-worker","status":201,"timestamp":"2025-07-19 05:02:08","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Analysis result saved to Archive Service","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"error","message":"Failed to update assessment job status","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Assessment job status updated","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"error","message":"Failed to update assessment job status","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","service":"analysis-worker","status":"completed","timestamp":"2025-07-19 05:02:08","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Assessment job status updated","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Analysis complete notification sent","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Analysis completion notification sent","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"159b5325-4424-45c3-aa56-b39d2613b260","level":"info","message":"Assessment job processed successfully","processingTime":"82081ms","resultId":"3e59c2f0-092a-4532-8505-c790324dfdd8","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"2c52e387-f790-4b6d-bbef-60c662a3f9e5","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Analysis complete notification sent","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Analysis completion notification sent","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"jobId":"c776ff0a-f2e5-4eaa-b9df-e231429ba989","level":"info","message":"Assessment job processed successfully","processingTime":"82085ms","resultId":"21e2dac1-a612-4a7f-b9d6-2d673e3e49ce","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-19 05:02:08","userId":"8a67b529-3099-4875-80c3-d4d60e7f669d","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:02:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:03:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:04:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:05:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:06:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:07:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:08:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:09:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:10:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:11:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:12:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:13:54","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:24","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-19 05:14:54","version":"1.0.0"}
